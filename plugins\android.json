{"prepare_queue": {"installed": [], "uninstalled": []}, "config_munge": {"files": {}}, "installed_plugins": {"com.olc.scan": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-hot-code-push-plugin": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-bd-geolocation": {"API_KEY": "uinN8rdNWfpZEtEFudy6GPOgd5UdZoZs", "PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-ble-central": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-bluetooth-serial": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-broadcaster": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-camera": {"ANDROID_SUPPORT_V4_VERSION": "27.+", "PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-canvas2image-updated": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-device": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-mipush": {"MI_PUSH_APP_KEY": "your_mipush_appkey", "MI_PUSH_APP_ID": "your_mipush_appid", "MI_PUSH_APP_IOS_KEY": "your_mipush_appkey", "MI_PUSH_APP_IOS_ID": "your_mipush_appid", "PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-splashscreen": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-statusbar": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-wechat": {"WECHATAPPID": "wx65acc367d9bcfbb3", "UNIVERSALLINK": "https://yingjiang168.com/", "PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-whitelist": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-x5-webview": {"PACKAGE_NAME": "com.yingjiang.app"}, "phonegap-plugin-barcodescanner": {"ANDROID_SUPPORT_V4_VERSION": "27.+", "PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-mlkit-barcode-scanner": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-androidx-adapter": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-inappbrowser": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-mqtt": {"PACKAGE_NAME": "com.yingjiang.app"}}, "dependent_plugins": {}}