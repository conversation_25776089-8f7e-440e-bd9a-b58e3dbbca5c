# This file was originally created by the Android Tools, but is now
# used by cordova-android to manage the state of the various third party
# libraries used in your application

# This is the Library Module that contains the Cordova Library, this is not
# required when using an AAR

# This is the application project.  This is only required for Android Studio Gradle projects

# Project target.
target=android-29
android.library.reference.1=CordovaLib
android.library.reference.2=app
cordova.gradle.include.1=cordova-hot-code-push-plugin/app-chcp.gradle
cordova.system.library.1=androidx.legacy:legacy-support-v4:1.0.0
cordova.gradle.include.2=phonegap-plugin-barcodescanner/app-barcodescanner.gradle
cordova.system.library.2=androidx.legacy:legacy-support-v4:1.0.0
cordova.gradle.include.3=cordova-plugin-wechat/app-android-build.gradle
cordova.system.library.3=androidx.legacy:legacy-support-v4:1.0.0
cordova.system.library.4=androidx.legacy:legacy-support-v4:1.0.0
cordova.system.library.5=com.google.android.material:material:1.2.0
cordova.system.library.6=com.google.android.gms:play-services-vision:17.0.2
cordova.system.library.7=com.google.mlkit:barcode-scanning:16.2.0
cordova.system.library.8=androidx.camera:camera-core:1.0.0-beta05
cordova.system.library.9=androidx.camera:camera-camera2:1.0.0-beta05
cordova.system.library.10=androidx.camera:camera-lifecycle:1.0.0-beta05
cordova.system.library.11=androidx.camera:camera-view:1.0.0-alpha12
cordova.system.library.12=androidx.constraintlayout:constraintlayout:2.0.4
cordova.gradle.include.4=cordova-plugin-mlkit-barcode-scanner/app-build-extras.gradle
cordova.system.library.13=org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.1.0