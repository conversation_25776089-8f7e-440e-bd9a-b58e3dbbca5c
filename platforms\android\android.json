{"prepare_queue": {"installed": [], "uninstalled": []}, "config_munge": {"files": {"config.xml": {"parents": {"/*": [{"xml": "<preference name=\"loadUrlTimeoutValue\" value=\"60000\" />", "count": 1}, {"xml": "<feature name=\"MiPushPlugin\"><param name=\"android-package\" value=\"com.ct.cordova.mipush.MiPushPlugin\" /></feature>", "count": 1}]}}, "res/xml/config.xml": {"parents": {"/*": [{"xml": "<feature name=\"HotCodePush\"><param name=\"android-package\" value=\"com.nordnetab.chcp.main.HotCodePushPlugin\" /><param name=\"onload\" value=\"true\" /></feature>", "count": 1}, {"xml": "<feature name=\"BaiduGeolocation\"><param name=\"android-package\" value=\"com.lai.geolocation.baidu.GeolocationPlugin\" /></feature>", "count": 1}, {"xml": "<feature name=\"Camera\"><param name=\"android-package\" value=\"org.apache.cordova.camera.CameraLauncher\" /></feature>", "count": 1}, {"xml": "<feature name=\"Device\"><param name=\"android-package\" value=\"org.apache.cordova.device.Device\" /></feature>", "count": 1}, {"xml": "<feature name=\"Whitelist\"><param name=\"android-package\" value=\"org.apache.cordova.whitelist.WhitelistPlugin\" /><param name=\"onload\" value=\"true\" /></feature>", "count": 1}, {"xml": "<preference name=\"webView\" value=\"org.jeremyup.cordova.x5engine.X5WebViewEngine\" />", "count": 1}, {"xml": "<feature name=\"Canvas2ImagePlugin\"><param name=\"android-package\" value=\"com.rodrigograca.canvas2image.Canvas2ImagePlugin\" /></feature>", "count": 1}, {"xml": "<feature name=\"StatusBar\"><param name=\"android-package\" value=\"org.apache.cordova.statusbar.StatusBar\" /><param name=\"onload\" value=\"true\" /></feature>", "count": 1}, {"xml": "<feature name=\"BarcodeScanner\"><param name=\"android-package\" value=\"com.phonegap.plugins.barcodescanner.BarcodeScanner\" /></feature>", "count": 1}, {"xml": "<feature name=\"SplashScreen\"><param name=\"android-package\" value=\"org.apache.cordova.splashscreen.SplashScreen\" /><param name=\"onload\" value=\"true\" /></feature>", "count": 1}, {"xml": "<feature name=\"Wechat\"><param name=\"android-package\" value=\"xu.li.cordova.wechat.Wechat\" /></feature>", "count": 1}, {"xml": "<preference name=\"WECHATAPPID\" value=\"wx65acc367d9bcfbb3\" />", "count": 1}, {"xml": "<feature name=\"scanplugin\"><param name=\"android-package\" value=\"com.olc.scan.scanplugin\" /><param name=\"onload\" value=\"true\" /></feature>", "count": 1}, {"xml": "<feature name=\"broadcaster\"><param name=\"android-package\" value=\"org.bsc.cordova.CDVBroadcaster\" /></feature>", "count": 1}, {"xml": "<feature name=\"cordova-plugin-mlkit-barcode-scanner\"><param name=\"android-package\" value=\"com.mobisys.cordova.plugins.mlkit.barcode.scanner.MLKitBarcodeScanner\" /></feature>", "count": 1}, {"xml": "<feature name=\"InAppBrowser\"><param name=\"android-package\" value=\"org.apache.cordova.inappbrowser.InAppBrowser\" /></feature>", "count": 1}, {"xml": "<feature name=\"CordovaMqTTPlugin\"><param name=\"android-package\" value=\"com.arcoirislabs.plugin.mqtt.CordovaMqTTPlugin\" /></feature>", "count": 1}], "/widget": [{"xml": "<feature name=\"BluetoothSerial\"><param name=\"android-package\" value=\"com.megster.cordova.BluetoothSerial\" /></feature>", "count": 1}, {"xml": "<feature name=\"BLE\"><param name=\"android-package\" value=\"com.megster.cordova.ble.central.BLECentralPlugin\" /></feature>", "count": 1}]}}, "AndroidManifest.xml": {"parents": {"/manifest": [{"xml": "<uses-permission android:name=\"android.permission.INTERNET\" />", "count": 2}, {"xml": "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />", "count": 2}, {"xml": "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\" />", "count": 2}, {"xml": "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.BLUETOOTH\" />", "count": 2}, {"xml": "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\" />", "count": 2}, {"xml": "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\" />", "count": 2}, {"xml": "<uses-permission android:name=\"android.permission.CAMERA\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.FLASHLIGHT\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.ACCESS_BACKGROUND_LOCATION\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.GET_TASKS\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.VIBRATE\" />", "count": 1}, {"xml": "<permission android:name=\"${applicationId}.permission.MIPUSH_RECEIVE\" android:protectionLevel=\"signature\" />", "count": 1}, {"xml": "<uses-permission android:name=\"${applicationId}.permission.MIPUSH_RECEIVE\" />", "count": 1}], "/*": [{"xml": "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\" />", "count": 3}, {"xml": "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />", "count": 3}, {"xml": "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\" />", "count": 3}, {"xml": "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />", "count": 5}, {"xml": "<uses-permission android:name=\"android.permission.INTERNET\" />", "count": 3}, {"xml": "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.WAKE_LOCK\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.CAMERA\" />", "count": 1}, {"xml": "<uses-feature android:name=\"android.hardware.camera\" android:required=\"false\" />", "count": 1}], "/manifest/application": [{"xml": "<service android:enabled=\"true\" android:name=\"com.baidu.location.f\" android:process=\":remote\" />", "count": 1}, {"xml": "<meta-data android:name=\"com.baidu.lbsapi.API_KEY\" android:value=\"uinN8rdNWfpZEtEFudy6GPOgd5UdZoZs\" />", "count": 1}, {"xml": "<activity android:clearTaskOnLaunch=\"true\" android:configChanges=\"orientation|keyboardHidden|screenSize\" android:exported=\"false\" android:name=\"com.google.zxing.client.android.CaptureActivity\" android:theme=\"@android:style/Theme.NoTitleBar.Fullscreen\" android:windowSoftInputMode=\"stateAlwaysHidden\" />", "count": 1}, {"xml": "<activity android:label=\"Share\" android:name=\"com.google.zxing.client.android.encode.EncodeActivity\" />", "count": 1}, {"xml": "<service android:enabled=\"true\" android:name=\"com.xiaomi.push.service.XMPushService\" android:process=\":pushservice\" />", "count": 1}, {"xml": "<service android:enabled=\"true\" android:exported=\"false\" android:name=\"com.xiaomi.push.service.XMJobService\" android:permission=\"android.permission.BIND_JOB_SERVICE\" android:process=\":pushservice\" />", "count": 1}, {"xml": "<service android:enabled=\"true\" android:exported=\"true\" android:name=\"com.xiaomi.mipush.sdk.PushMessageHandler\" />", "count": 1}, {"xml": "<service android:enabled=\"true\" android:name=\"com.xiaomi.mipush.sdk.MessageHandleService\" />", "count": 1}, {"xml": "<receiver android:exported=\"true\" android:name=\"com.xiaomi.push.service.receivers.NetworkStatusReceiver\"><intent-filter><action android:name=\"android.net.conn.CONNECTIVITY_CHANGE\" /><category android:name=\"android.intent.category.DEFAULT\" /></intent-filter></receiver>", "count": 1}, {"xml": "<receiver android:exported=\"false\" android:name=\"com.xiaomi.push.service.receivers.PingReceiver\" android:process=\":pushservice\"><intent-filter><action android:name=\"com.xiaomi.push.PING_TIMER\" /></intent-filter></receiver>", "count": 1}, {"xml": "<receiver android:exported=\"true\" android:name=\"com.ct.cordova.mipush.MiPushReceiver\"><intent-filter><action android:name=\"com.xiaomi.mipush.RECEIVE_MESSAGE\" /></intent-filter><intent-filter><action android:name=\"com.xiaomi.mipush.MESSAGE_ARRIVED\" /></intent-filter><intent-filter><action android:name=\"com.xiaomi.mipush.ERROR\" /></intent-filter></receiver>", "count": 1}, {"xml": "<meta-data android:name=\"<PERSON><PERSON>ushApp<PERSON><PERSON>\" android:value=\"your_mipush_appkeymipush\" />", "count": 1}, {"xml": "<meta-data android:name=\"MiPushAppId\" android:value=\"your_mipush_appidmipush\" />", "count": 1}, {"xml": "<activity android:exported=\"true\" android:label=\"@string/launcher_name\" android:launchMode=\"singleTask\" android:name=\".wxapi.WXEntryActivity\" android:taskAffinity=\"com.yingjiang.app\"><intent-filter><action android:name=\"android.intent.action.VIEW\" /><category android:name=\"android.intent.category.DEFAULT\" /><data android:scheme=\"wx65acc367d9bcfbb3\" /></intent-filter></activity>", "count": 1}, {"xml": "<activity android:exported=\"true\" android:label=\"@string/launcher_name\" android:launchMode=\"singleTop\" android:name=\".wxapi.WXPayEntryActivity\"><intent-filter><action android:name=\"android.intent.action.VIEW\" /><category android:name=\"android.intent.category.DEFAULT\" /><data android:scheme=\"wx65acc367d9bcfbb3\" /></intent-filter></activity>", "count": 1}], "application": [{"xml": "<provider android:authorities=\"${applicationId}.cordova.plugin.camera.provider\" android:exported=\"false\" android:grantUriPermissions=\"true\" android:name=\"org.apache.cordova.camera.FileProvider\"><meta-data android:name=\"android.support.FILE_PROVIDER_PATHS\" android:resource=\"@xml/camera_provider_paths\" /></provider>", "count": 1}, {"xml": "<meta-data android:name=\"com.google.android.gms.version\" android:value=\"@integer/google_play_services_version\" />", "count": 1}, {"xml": "<meta-data android:name=\"com.google.android.gms.vision.DEPENDENCIES\" android:value=\"barcode\" />", "count": 1}, {"xml": "<activity android:label=\"Read Barcode\" android:name=\"com.mobisys.cordova.plugins.mlkit.barcode.scanner.CaptureActivity\" android:theme=\"@style/Theme.AppCompat.Light.NoActionBar\" />", "count": 1}]}}, "res/values/strings.xml": {"parents": {"/*": []}}}}, "installed_plugins": {"cordova-hot-code-push-plugin": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-bd-geolocation": {"API_KEY": "uinN8rdNWfpZEtEFudy6GPOgd5UdZoZs", "PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-bluetooth-serial": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-camera": {"ANDROID_SUPPORT_V4_VERSION": "27.+", "PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-device": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-whitelist": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-x5-webview": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-canvas2image-updated": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-statusbar": {"PACKAGE_NAME": "com.yingjiang.app"}, "phonegap-plugin-barcodescanner": {"ANDROID_SUPPORT_V4_VERSION": "27.+", "PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-ble-central": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-splashscreen": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-mipush": {"MI_PUSH_APP_KEY": "your_mipush_appkey", "MI_PUSH_APP_ID": "your_mipush_appid", "MI_PUSH_APP_IOS_KEY": "your_mipush_appkey", "MI_PUSH_APP_IOS_ID": "your_mipush_appid", "PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-wechat": {"WECHATAPPID": "wx65acc367d9bcfbb3", "UNIVERSALLINK": "http://yingjiang168.com/Login", "PACKAGE_NAME": "com.yingjiang.app"}, "com.olc.scan": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-broadcaster": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-androidx-adapter": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-mlkit-barcode-scanner": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-inappbrowser": {"PACKAGE_NAME": "com.yingjiang.app"}, "cordova-plugin-mqtt": {"PACKAGE_NAME": "com.yingjiang.app"}}, "dependent_plugins": {}, "modules": [{"id": "cordova-hot-code-push-plugin.chcp", "file": "plugins/cordova-hot-code-push-plugin/www/chcp.js", "pluginId": "cordova-hot-code-push-plugin", "clobbers": ["chcp"]}, {"id": "cordova-plugin-bd-geolocation.Geolocation", "file": "plugins/cordova-plugin-bd-geolocation/www/Geolocation.js", "pluginId": "cordova-plugin-bd-geolocation", "clobbers": ["window.navigator.geolocation"]}, {"id": "cordova-plugin-bluetooth-serial.bluetoothSerial", "file": "plugins/cordova-plugin-bluetooth-serial/www/bluetoothSerial.js", "pluginId": "cordova-plugin-bluetooth-serial", "clobbers": ["window.bluetoothSerial"]}, {"id": "cordova-plugin-camera.Camera", "file": "plugins/cordova-plugin-camera/www/CameraConstants.js", "pluginId": "cordova-plugin-camera", "clobbers": ["Camera"]}, {"id": "cordova-plugin-camera.CameraPopoverOptions", "file": "plugins/cordova-plugin-camera/www/CameraPopoverOptions.js", "pluginId": "cordova-plugin-camera", "clobbers": ["CameraPopoverOptions"]}, {"id": "cordova-plugin-camera.camera", "file": "plugins/cordova-plugin-camera/www/Camera.js", "pluginId": "cordova-plugin-camera", "clobbers": ["navigator.camera"]}, {"id": "cordova-plugin-camera.CameraPopoverHandle", "file": "plugins/cordova-plugin-camera/www/CameraPopoverHandle.js", "pluginId": "cordova-plugin-camera", "clobbers": ["CameraPopoverHandle"]}, {"id": "cordova-plugin-bluetooth-serial.bluetoothSerial", "file": "plugins/cordova-plugin-bluetooth-serial/www/bluetoothSerial.js", "pluginId": "cordova-plugin-bluetooth-serial", "clobbers": ["window.bluetoothSerial"]}, {"id": "cordova-hot-code-push-plugin.chcp", "file": "plugins/cordova-hot-code-push-plugin/www/chcp.js", "pluginId": "cordova-hot-code-push-plugin", "clobbers": ["chcp"]}, {"id": "cordova-plugin-device.device", "file": "plugins/cordova-plugin-device/www/device.js", "pluginId": "cordova-plugin-device", "clobbers": ["device"]}, {"id": "cordova-plugin-canvas2image-updated.Canvas2ImagePlugin", "file": "plugins/cordova-plugin-canvas2image-updated/www/Canvas2ImagePlugin.js", "pluginId": "cordova-plugin-canvas2image-updated", "clobbers": ["window.canvas2ImagePlugin"]}, {"id": "cordova-plugin-statusbar.statusbar", "file": "plugins/cordova-plugin-statusbar/www/statusbar.js", "pluginId": "cordova-plugin-statusbar", "clobbers": ["window.StatusBar"]}, {"id": "phonegap-plugin-barcodescanner.BarcodeScanner", "file": "plugins/phonegap-plugin-barcodescanner/www/barcodescanner.js", "pluginId": "phonegap-plugin-barcodescanner", "clobbers": ["cordova.plugins.barcodeScanner"]}, {"id": "cordova-plugin-ble-central.ble", "file": "plugins/cordova-plugin-ble-central/www/ble.js", "pluginId": "cordova-plugin-ble-central", "clobbers": ["ble"]}, {"id": "cordova-plugin-splashscreen.SplashScreen", "file": "plugins/cordova-plugin-splashscreen/www/splashscreen.js", "pluginId": "cordova-plugin-splashscreen", "clobbers": ["navigator.splashscreen"]}, {"id": "cordova-plugin-mi<PERSON><PERSON>.<PERSON><PERSON><PERSON>", "file": "plugins/cordova-plugin-mipush/www/MiPush.js", "pluginId": "cordova-plugin-mipush", "clobbers": ["<PERSON><PERSON><PERSON>"]}, {"id": "cordova-plugin-wechat.Wechat", "file": "plugins/cordova-plugin-wechat/www/wechat.js", "pluginId": "cordova-plugin-wechat", "clobbers": ["Wechat"]}, {"id": "com.olc.scan.scanplugin", "file": "plugins/com.olc.scan/www/scanplugin.js", "pluginId": "com.olc.scan", "clobbers": ["cordova.plugins.scanplugin"]}, {"id": "cordova-plugin-broadcaster.broadcaster", "file": "plugins/cordova-plugin-broadcaster/www/broadcaster.js", "pluginId": "cordova-plugin-broadcaster", "clobbers": ["broadcaster"]}, {"id": "cordova-plugin-mlkit-barcode-scanner.cordova-plugin-ml-kit-barcode-scanner", "file": "plugins/cordova-plugin-mlkit-barcode-scanner/www/BarcodeScanner.plugin.js", "pluginId": "cordova-plugin-mlkit-barcode-scanner", "clobbers": ["cordova.plugins.mlkit.barcodeScanner"]}, {"id": "cordova-plugin-inappbrowser.inappbrowser", "file": "plugins/cordova-plugin-inappbrowser/www/inappbrowser.js", "pluginId": "cordova-plugin-inappbrowser", "clobbers": ["cordova.InAppBrowser.open"]}, {"id": "cordova-plugin-mqtt.MQTTEmitter", "file": "plugins/cordova-plugin-mqtt/www/MQTTEmitter.js", "pluginId": "cordova-plugin-mqtt", "clobbers": ["ME"]}, {"id": "cordova-plugin-mqtt.CordovaMqTTPlugin", "file": "plugins/cordova-plugin-mqtt/www/cordova-plugin-mqtt.js", "pluginId": "cordova-plugin-mqtt", "clobbers": ["cordova.plugins.CordovaMqTTPlugin"]}], "plugin_metadata": {"cordova-hot-code-push-plugin": "1.5.3", "cordova-plugin-bd-geolocation": "8.5.0", "cordova-plugin-bluetooth-serial": "0.4.7", "cordova-plugin-camera": "5.0.1", "cordova-plugin-device": "2.0.3", "cordova-plugin-whitelist": "1.3.4", "cordova-plugin-x5-webview": "3.1.0", "cordova-plugin-canvas2image-updated": "1.2.0", "cordova-plugin-statusbar": "2.4.3", "phonegap-plugin-barcodescanner": "8.1.0", "cordova-plugin-ble-central": "1.3.1", "cordova-plugin-splashscreen": "6.0.0", "cordova-plugin-mipush": "2.0.3", "cordova-plugin-wechat": "3.0.0", "com.olc.scan": "1.0.1", "cordova-plugin-broadcaster": "4.3.1", "cordova-plugin-androidx-adapter": "1.1.3", "cordova-plugin-mlkit-barcode-scanner": "3.0.7", "cordova-plugin-inappbrowser": "6.0.1-dev", "cordova-plugin-mqtt": "0.3.8"}}