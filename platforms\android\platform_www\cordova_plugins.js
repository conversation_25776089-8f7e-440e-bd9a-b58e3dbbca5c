cordova.define('cordova/plugin_list', function(require, exports, module) {
  module.exports = [
    {
      "id": "cordova-hot-code-push-plugin.chcp",
      "file": "plugins/cordova-hot-code-push-plugin/www/chcp.js",
      "pluginId": "cordova-hot-code-push-plugin",
      "clobbers": [
        "chcp"
      ]
    },
    {
      "id": "cordova-plugin-bd-geolocation.Geolocation",
      "file": "plugins/cordova-plugin-bd-geolocation/www/Geolocation.js",
      "pluginId": "cordova-plugin-bd-geolocation",
      "clobbers": [
        "window.navigator.geolocation"
      ]
    },
    {
      "id": "cordova-plugin-bluetooth-serial.bluetoothSerial",
      "file": "plugins/cordova-plugin-bluetooth-serial/www/bluetoothSerial.js",
      "pluginId": "cordova-plugin-bluetooth-serial",
      "clobbers": [
        "window.bluetoothSerial"
      ]
    },
    {
      "id": "cordova-plugin-camera.Camera",
      "file": "plugins/cordova-plugin-camera/www/CameraConstants.js",
      "pluginId": "cordova-plugin-camera",
      "clobbers": [
        "Camera"
      ]
    },
    {
      "id": "cordova-plugin-camera.CameraPopoverOptions",
      "file": "plugins/cordova-plugin-camera/www/CameraPopoverOptions.js",
      "pluginId": "cordova-plugin-camera",
      "clobbers": [
        "CameraPopoverOptions"
      ]
    },
    {
      "id": "cordova-plugin-camera.camera",
      "file": "plugins/cordova-plugin-camera/www/Camera.js",
      "pluginId": "cordova-plugin-camera",
      "clobbers": [
        "navigator.camera"
      ]
    },
    {
      "id": "cordova-plugin-camera.CameraPopoverHandle",
      "file": "plugins/cordova-plugin-camera/www/CameraPopoverHandle.js",
      "pluginId": "cordova-plugin-camera",
      "clobbers": [
        "CameraPopoverHandle"
      ]
    },
    {
      "id": "cordova-plugin-bluetooth-serial.bluetoothSerial",
      "file": "plugins/cordova-plugin-bluetooth-serial/www/bluetoothSerial.js",
      "pluginId": "cordova-plugin-bluetooth-serial",
      "clobbers": [
        "window.bluetoothSerial"
      ]
    },
    {
      "id": "cordova-hot-code-push-plugin.chcp",
      "file": "plugins/cordova-hot-code-push-plugin/www/chcp.js",
      "pluginId": "cordova-hot-code-push-plugin",
      "clobbers": [
        "chcp"
      ]
    },
    {
      "id": "cordova-plugin-device.device",
      "file": "plugins/cordova-plugin-device/www/device.js",
      "pluginId": "cordova-plugin-device",
      "clobbers": [
        "device"
      ]
    },
    {
      "id": "cordova-plugin-canvas2image-updated.Canvas2ImagePlugin",
      "file": "plugins/cordova-plugin-canvas2image-updated/www/Canvas2ImagePlugin.js",
      "pluginId": "cordova-plugin-canvas2image-updated",
      "clobbers": [
        "window.canvas2ImagePlugin"
      ]
    },
    {
      "id": "cordova-plugin-statusbar.statusbar",
      "file": "plugins/cordova-plugin-statusbar/www/statusbar.js",
      "pluginId": "cordova-plugin-statusbar",
      "clobbers": [
        "window.StatusBar"
      ]
    },
    {
      "id": "phonegap-plugin-barcodescanner.BarcodeScanner",
      "file": "plugins/phonegap-plugin-barcodescanner/www/barcodescanner.js",
      "pluginId": "phonegap-plugin-barcodescanner",
      "clobbers": [
        "cordova.plugins.barcodeScanner"
      ]
    },
    {
      "id": "cordova-plugin-ble-central.ble",
      "file": "plugins/cordova-plugin-ble-central/www/ble.js",
      "pluginId": "cordova-plugin-ble-central",
      "clobbers": [
        "ble"
      ]
    },
    {
      "id": "cordova-plugin-splashscreen.SplashScreen",
      "file": "plugins/cordova-plugin-splashscreen/www/splashscreen.js",
      "pluginId": "cordova-plugin-splashscreen",
      "clobbers": [
        "navigator.splashscreen"
      ]
    },
    {
      "id": "cordova-plugin-mipush.MiPush",
      "file": "plugins/cordova-plugin-mipush/www/MiPush.js",
      "pluginId": "cordova-plugin-mipush",
      "clobbers": [
        "MiPush"
      ]
    },
    {
      "id": "cordova-plugin-wechat.Wechat",
      "file": "plugins/cordova-plugin-wechat/www/wechat.js",
      "pluginId": "cordova-plugin-wechat",
      "clobbers": [
        "Wechat"
      ]
    },
    {
      "id": "com.olc.scan.scanplugin",
      "file": "plugins/com.olc.scan/www/scanplugin.js",
      "pluginId": "com.olc.scan",
      "clobbers": [
        "cordova.plugins.scanplugin"
      ]
    },
    {
      "id": "cordova-plugin-broadcaster.broadcaster",
      "file": "plugins/cordova-plugin-broadcaster/www/broadcaster.js",
      "pluginId": "cordova-plugin-broadcaster",
      "clobbers": [
        "broadcaster"
      ]
    },
    {
      "id": "cordova-plugin-mlkit-barcode-scanner.cordova-plugin-ml-kit-barcode-scanner",
      "file": "plugins/cordova-plugin-mlkit-barcode-scanner/www/BarcodeScanner.plugin.js",
      "pluginId": "cordova-plugin-mlkit-barcode-scanner",
      "clobbers": [
        "cordova.plugins.mlkit.barcodeScanner"
      ]
    },
    {
      "id": "cordova-plugin-inappbrowser.inappbrowser",
      "file": "plugins/cordova-plugin-inappbrowser/www/inappbrowser.js",
      "pluginId": "cordova-plugin-inappbrowser",
      "clobbers": [
        "cordova.InAppBrowser.open"
      ]
    },
    {
      "id": "cordova-plugin-mqtt.MQTTEmitter",
      "file": "plugins/cordova-plugin-mqtt/www/MQTTEmitter.js",
      "pluginId": "cordova-plugin-mqtt",
      "clobbers": [
        "ME"
      ]
    },
    {
      "id": "cordova-plugin-mqtt.CordovaMqTTPlugin",
      "file": "plugins/cordova-plugin-mqtt/www/cordova-plugin-mqtt.js",
      "pluginId": "cordova-plugin-mqtt",
      "clobbers": [
        "cordova.plugins.CordovaMqTTPlugin"
      ]
    }
  ];
  module.exports.metadata = {
    "cordova-hot-code-push-plugin": "1.5.3",
    "cordova-plugin-bd-geolocation": "8.5.0",
    "cordova-plugin-bluetooth-serial": "0.4.7",
    "cordova-plugin-camera": "5.0.1",
    "cordova-plugin-device": "2.0.3",
    "cordova-plugin-whitelist": "1.3.4",
    "cordova-plugin-x5-webview": "3.1.0",
    "cordova-plugin-canvas2image-updated": "1.2.0",
    "cordova-plugin-statusbar": "2.4.3",
    "phonegap-plugin-barcodescanner": "8.1.0",
    "cordova-plugin-ble-central": "1.3.1",
    "cordova-plugin-splashscreen": "6.0.0",
    "cordova-plugin-mipush": "2.0.3",
    "cordova-plugin-wechat": "3.0.0",
    "com.olc.scan": "1.0.1",
    "cordova-plugin-broadcaster": "4.3.1",
    "cordova-plugin-androidx-adapter": "1.1.3",
    "cordova-plugin-mlkit-barcode-scanner": "3.0.7",
    "cordova-plugin-inappbrowser": "6.0.1-dev",
    "cordova-plugin-mqtt": "0.3.8"
  };
});