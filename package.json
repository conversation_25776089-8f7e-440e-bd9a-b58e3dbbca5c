{"name": "com.yingjiang.app", "displayName": "营匠", "version": "1.0.1", "description": "A sample Apache Cordova application that responds to the deviceready event.", "main": "index.js", "scripts": {"dev": "SET NODE_OPTIONS=--openssl-legacy-provider &&vue-cli-service serve --mode development --sourcemap", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ecosystem:cordova"], "author": "Apache Cordova Team", "license": "Apache-2.0", "devDependencies": {"@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-service": "^5.0.8", "babel-plugin-transform-es2015-arrow-functions": "^6.22.0", "compression-webpack-plugin": "^10.0.0", "cordova-android": "^9.0.0", "cordova-hot-code-push-plugin": "github:nordnet/cordova-hot-code-push", "cordova-ios": "^6.1.1", "cordova-plugin-ble-central": "^1.3.1", "cordova-plugin-bluetooth-serial": "^0.4.7", "cordova-plugin-broadcaster": "^4.3.1", "cordova-plugin-camera": "^5.0.1", "cordova-plugin-canvas2image-updated": "^1.2.0", "cordova-plugin-compat": "^1.2.0", "cordova-plugin-inappbrowser": "github:apache/cordova-plugin-inappbrowser", "cordova-plugin-mipush": "^2.0.3", "cordova-plugin-mqtt": "^0.3.8", "cordova-plugin-splashscreen": "^6.0.0", "cordova-plugin-statusbar": "^2.4.3", "cordova-plugin-whitelist": "^1.3.4", "cordova-plugin-x5-webview": "^3.1.3", "phonegap-plugin-barcodescanner": "^8.1.0", "rimraf": "^3.0.2", "scanplugin": "file:../../phonegapscan/plugins/com.olc.scan", "style-resources-loader": "^1.5.0", "vue-cli-plugin-style-resources-loader": "^0.1.5"}, "cordova": {"plugins": {"cordova-plugin-whitelist": {}, "cordova-plugin-bd-geolocation": {"API_KEY": "uinN8rdNWfpZEtEFudy6GPOgd5UdZoZs"}, "cordova-plugin-camera": {}, "cordova-plugin-bluetooth-serial": {}, "cordova-hot-code-push-plugin": {}, "cordova-plugin-device": {}, "cordova-plugin-x5-webview": {}, "cordova-plugin-canvas2image-updated": {"PHOTOLIBRARY_USAGE_DESCRIPTION": " ", "PHOTOLIBRARY_ADD_USAGE_DESCRIPTION": " "}, "cordova-plugin-statusbar": {}, "phonegap-plugin-barcodescanner": {"ANDROID_SUPPORT_V4_VERSION": "27.+"}, "cordova-plugin-ble-central": {}, "cordova-plugin-mipush": {"MI_PUSH_APP_KEY": "your_mipush_appkey", "MI_PUSH_APP_ID": "your_mipush_appid", "MI_PUSH_APP_IOS_ID": "your_mipush_appid", "MI_PUSH_APP_IOS_KEY": "your_mipush_appkey"}, "com.olc.scan": {}, "cordova-plugin-broadcaster": {}}, "platforms": ["android"]}, "dependencies": {"@vue/cli-shared-utils": "^4.5.9", "animate.css": "^4.1.1", "axios": "^1.7.7", "better-scroll": "^2.5.1", "clipboard": "^2.0.11", "cnpm": "^6.1.1", "coordtransform": "^2.1.2", "cordova-lib": "^10.0.0", "cordova-plugin-bd-geolocation": "8.5.0", "cropperjs": "^1.6.2", "hammerjs": "^2.0.8", "image-compressor.js": "^1.1.4", "jquery": "^3.5.1", "js-md5": "^0.8.3", "jssha": "^3.2.0", "qrcodejs": "^1.0.0", "qrcodejs2": "^0.0.2", "recursive-readdir": "^2.2.3", "stylus-loader": "^3.0.2", "vue-baidu-map": "^0.21.22", "vue-slicksort": "^1.2.0", "vue2-datepicker": "^3.11.1", "vuedraggable": "^2.24.3"}}