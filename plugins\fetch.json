{"cordova-plugin-whitelist": {"source": {"type": "registry", "id": "cordova-plugin-whitelist@^1.3.4"}, "is_top_level": true, "variables": {}}, "cordova-plugin-bd-geolocation": {"source": {"type": "registry", "id": "cordova-plugin-bd-geolocation"}, "is_top_level": true, "variables": {"API_KEY": "uinN8rdNWfpZEtEFudy6GPOgd5UdZoZs"}}, "cordova-plugin-camera": {"source": {"type": "registry", "id": "cordova-plugin-camera@^5.0.1"}, "is_top_level": true, "variables": {}}, "cordova-plugin-bluetooth-serial": {"source": {"type": "registry", "id": "cordova-plugin-bluetooth-serial"}, "is_top_level": true, "variables": {}}, "cordova-hot-code-push-plugin": {"source": {"type": "registry", "id": "https://github.com/nordnet/cordova-hot-code-push.git"}, "is_top_level": true, "variables": {}}, "cordova-plugin-device": {"source": {"type": "registry", "id": "cordova-plugin-device"}, "is_top_level": true, "variables": {}}, "cordova-plugin-x5-webview": {"source": {"type": "registry", "id": "cordova-plugin-x5-webview"}, "is_top_level": true, "variables": {}}, "cordova-plugin-canvas2image-updated": {"source": {"type": "registry", "id": "cordova-plugin-canvas2image-updated"}, "is_top_level": true, "variables": {}}, "cordova-plugin-compat": {"source": {"type": "registry", "id": "cordova-plugin-compat@^1.0.0"}, "is_top_level": false, "variables": {}}, "cordova-plugin-statusbar": {"source": {"type": "registry", "id": "cordova-plugin-statusbar@^2.4.3"}, "is_top_level": true, "variables": {}}, "phonegap-plugin-barcodescanner": {"source": {"type": "registry", "id": "phonegap-plugin-barcodescanner@8.1.0"}, "is_top_level": true, "variables": {}}, "cordova-plugin-ble-central": {"source": {"type": "registry", "id": "cordova-plugin-ble-central"}, "is_top_level": true, "variables": {}}, "cordova-plugin-splashscreen": {"source": {"type": "registry", "id": "cordova-plugin-splashscreen@^6.0.0"}, "is_top_level": true, "variables": {}}, "cordova-plugin-mipush": {"source": {"type": "registry", "id": "cordova-plugin-mipush"}, "is_top_level": true, "variables": {"MI_PUSH_APP_KEY": "your_mipush_appkey", "MI_PUSH_APP_ID": "your_mipush_appid", "MI_PUSH_APP_IOS_ID": "your_mipush_appid", "MI_PUSH_APP_IOS_KEY": "your_mipush_appkey"}}, "cordova-plugin-wechat": {"source": {"type": "registry", "id": "cordova-plugin-wechat@3.0.0"}, "is_top_level": true, "variables": {"WECHATAPPID": "wx65acc367d9bcfbb3", "UNIVERSALLINK": "https://yingjiang168.com/"}}, "com.olc.scan": {"source": {"type": "local", "path": "F:\\yingjiang\\YingJiangApp\\ying-jiang-app\\node_modules\\scanplugin"}, "is_top_level": true, "variables": {}}, "cordova-plugin-broadcaster": {"source": {"type": "registry", "id": "cordova-plugin-broadcaster@4.3.1"}, "is_top_level": true, "variables": {}}, "cordova-plugin-mlkit-barcode-scanner": {"source": {"type": "registry", "id": "cordova-plugin-mlkit-barcode-scanner"}, "is_top_level": true, "variables": {}}, "cordova-plugin-androidx-adapter": {"source": {"type": "registry", "id": "cordova-plugin-androidx-adapter"}, "is_top_level": true, "variables": {}}, "cordova-plugin-inappbrowser": {"source": {"type": "registry", "id": "https://github.com/apache/cordova-plugin-inappbrowser.git"}, "is_top_level": true, "variables": {}}, "cordova-plugin-mqtt": {"source": {"type": "registry", "id": "cordova-plugin-mqtt@^0.3.8"}, "is_top_level": true, "variables": {}}}